<template>
  <div class="workflow-template-test p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-4">工作流模板测试</h1>
      <p class="text-gray-600 mb-4">测试不同的工作流模板创建功能</p>
      
      <div class="flex gap-4 mb-6">
        <el-button @click="createTemplate('default')" type="primary">
          创建完整工作流模板
        </el-button>
        <el-button @click="createTemplate('simple')" type="success">
          创建简单工作流模板
        </el-button>
        <el-button @click="createTemplate('empty')" type="info">
          创建空白工作流
        </el-button>
      </div>
      
      <div v-if="currentTemplate" class="mb-4">
        <h3 class="text-lg font-semibold mb-2">当前模板信息：</h3>
        <div class="bg-gray-100 p-4 rounded">
          <p><strong>名称：</strong>{{ currentTemplate.name }}</p>
          <p><strong>描述：</strong>{{ currentTemplate.description }}</p>
          <p><strong>节点数量：</strong>{{ currentTemplate.nodes.length }}</p>
          <p><strong>连接数量：</strong>{{ currentTemplate.edges.length }}</p>
          <p><strong>类别：</strong>{{ currentTemplate.metadata.category }}</p>
          <p><strong>标签：</strong>{{ currentTemplate.metadata.tags.join(', ') }}</p>
        </div>
      </div>
    </div>

    <!-- 工作流编辑器 -->
    <div class="h-600px border border-gray-300 rounded">
      <WorkflowEditor
        v-if="currentTemplate"
        v-model="currentTemplate"
        @save="onSave"
        @update:modelValue="onUpdate"
      />
      <div v-else class="flex items-center justify-center h-full text-gray-500">
        请选择一个模板来开始
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import WorkflowEditor from '@/components/workflow/WorkflowEditor.vue';
  import { useWorkflow } from '@/composables/useWorkflow';
  import type { WorkflowDefinition } from '@/types/workflow';

  const { createWorkflow, saveWorkflow } = useWorkflow();
  const currentTemplate = ref<WorkflowDefinition | null>(null);

  const createTemplate = (type: 'default' | 'simple' | 'empty') => {
    const templateNames = {
      default: '完整数据处理工作流',
      simple: '简单三步工作流',
      empty: '空白工作流'
    };
    
    const templateDescriptions = {
      default: '包含数据输入、验证、处理、条件判断和结果输出的完整工作流',
      simple: '包含输入、处理、输出三个基本步骤的简单工作流',
      empty: '空白工作流，可以自由添加节点'
    };

    try {
      const workflow = createWorkflow(
        templateNames[type],
        templateDescriptions[type],
        type
      );
      
      currentTemplate.value = workflow;
      ElMessage.success(`${templateNames[type]}创建成功！`);
      
      // 输出模板信息到控制台，方便调试
      console.log(`创建了${templateNames[type]}:`, workflow);
    } catch (error) {
      console.error('创建工作流模板失败:', error);
      ElMessage.error('创建工作流模板失败');
    }
  };

  const onSave = (workflow: WorkflowDefinition) => {
    try {
      saveWorkflow(workflow);
      ElMessage.success('工作流保存成功');
    } catch (error) {
      console.error('保存工作流失败:', error);
      ElMessage.error('保存工作流失败');
    }
  };

  const onUpdate = (workflow: WorkflowDefinition) => {
    currentTemplate.value = workflow;
  };
</script>

<style scoped>
  .workflow-template-test {
    max-width: 1400px;
    margin: 0 auto;
  }
</style>
