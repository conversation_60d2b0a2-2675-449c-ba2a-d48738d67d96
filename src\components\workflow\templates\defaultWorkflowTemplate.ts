import type { WorkflowDefinition, NodeData, EdgeData } from '@/types/workflow';

/**
 * 创建默认工作流模板
 * 包含一个完整的数据处理流程：输入 -> 处理 -> 输出
 */
export function createDefaultWorkflowTemplate(name: string, description?: string): WorkflowDefinition {
  const timestamp = Date.now();
  
  // 创建默认节点
  const nodes: NodeData[] = [
    // 输入节点
    {
      id: `input-${timestamp}`,
      type: 'input',
      label: '数据输入',
      icon: 'i-mdi:text-box',
      config: {
        placeholder: '请输入要处理的数据',
        multiline: true,
        maxLength: 5000,
        dataType: 'text'
      },
      inputs: [],
      outputs: [
        {
          id: 'data',
          name: '输入数据',
          type: 'string',
          required: true,
          description: '用户输入的原始数据'
        }
      ],
      position: { x: 100, y: 200 },
      metadata: {
        description: '接收用户输入的数据，支持文本、数字等多种格式',
        tags: ['输入', '数据源'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 数据验证节点
    {
      id: `validation-${timestamp}`,
      type: 'process',
      label: '数据验证',
      icon: 'i-mdi:check-circle',
      config: {
        validationType: 'format',
        required: true,
        minLength: 1,
        maxLength: 1000,
        pattern: ''
      },
      inputs: [
        {
          id: 'input_data',
          name: '原始数据',
          type: 'string',
          required: true,
          description: '需要验证的数据'
        }
      ],
      outputs: [
        {
          id: 'validated_data',
          name: '验证后数据',
          type: 'string',
          required: true,
          description: '通过验证的数据'
        },
        {
          id: 'validation_result',
          name: '验证结果',
          type: 'boolean',
          required: true,
          description: '数据是否通过验证'
        }
      ],
      position: { x: 400, y: 200 },
      metadata: {
        description: '验证输入数据的格式和有效性，确保数据质量',
        tags: ['验证', '数据处理'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 数据处理节点
    {
      id: `process-${timestamp}`,
      type: 'process',
      label: '数据处理',
      icon: 'i-mdi:cog',
      config: {
        processType: 'transform',
        operation: 'format',
        outputFormat: 'json',
        enableCache: true
      },
      inputs: [
        {
          id: 'raw_data',
          name: '原始数据',
          type: 'string',
          required: true,
          description: '需要处理的数据'
        }
      ],
      outputs: [
        {
          id: 'processed_data',
          name: '处理结果',
          type: 'object',
          required: true,
          description: '处理后的数据对象'
        },
        {
          id: 'metadata',
          name: '处理信息',
          type: 'object',
          required: false,
          description: '处理过程的元数据信息'
        }
      ],
      position: { x: 700, y: 200 },
      metadata: {
        description: '对验证后的数据进行转换、格式化等处理操作',
        tags: ['处理', '转换'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 条件判断节点
    {
      id: `condition-${timestamp}`,
      type: 'condition',
      label: '结果判断',
      icon: 'i-mdi:help-rhombus',
      config: {
        conditionType: 'expression',
        expression: 'processed_data.length > 0',
        trueLabel: '处理成功',
        falseLabel: '处理失败'
      },
      inputs: [
        {
          id: 'data',
          name: '处理结果',
          type: 'object',
          required: true,
          description: '需要判断的数据'
        }
      ],
      outputs: [
        {
          id: 'success',
          name: '成功分支',
          type: 'object',
          required: false,
          description: '条件为真时的输出'
        },
        {
          id: 'failure',
          name: '失败分支',
          type: 'object',
          required: false,
          description: '条件为假时的输出'
        }
      ],
      position: { x: 1000, y: 200 },
      metadata: {
        description: '根据处理结果判断是否成功，决定后续流程',
        tags: ['条件', '分支'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 成功输出节点
    {
      id: `output-success-${timestamp}`,
      type: 'output',
      label: '成功输出',
      icon: 'i-mdi:check-circle',
      config: {
        outputFormat: 'json',
        includeMetadata: true,
        successMessage: '数据处理完成'
      },
      inputs: [
        {
          id: 'result',
          name: '处理结果',
          type: 'object',
          required: true,
          description: '最终的处理结果'
        }
      ],
      outputs: [],
      position: { x: 1300, y: 100 },
      metadata: {
        description: '输出成功处理的结果数据',
        tags: ['输出', '成功'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 失败输出节点
    {
      id: `output-error-${timestamp}`,
      type: 'output',
      label: '错误输出',
      icon: 'i-mdi:alert-circle',
      config: {
        outputFormat: 'json',
        includeError: true,
        errorMessage: '数据处理失败'
      },
      inputs: [
        {
          id: 'error',
          name: '错误信息',
          type: 'object',
          required: true,
          description: '错误详情和原因'
        }
      ],
      outputs: [],
      position: { x: 1300, y: 300 },
      metadata: {
        description: '输出处理失败的错误信息',
        tags: ['输出', '错误'],
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    }
  ];
  
  // 创建连接线
  const edges: EdgeData[] = [
    // 输入 -> 验证
    {
      id: `edge-input-validation-${timestamp}`,
      source: `input-${timestamp}`,
      target: `validation-${timestamp}`,
      sourceHandle: 'data',
      targetHandle: 'input_data',
      label: 'string',
      animated: true
    },
    
    // 验证 -> 处理
    {
      id: `edge-validation-process-${timestamp}`,
      source: `validation-${timestamp}`,
      target: `process-${timestamp}`,
      sourceHandle: 'validated_data',
      targetHandle: 'raw_data',
      label: 'string',
      animated: true
    },
    
    // 处理 -> 条件判断
    {
      id: `edge-process-condition-${timestamp}`,
      source: `process-${timestamp}`,
      target: `condition-${timestamp}`,
      sourceHandle: 'processed_data',
      targetHandle: 'data',
      label: 'object',
      animated: true
    },
    
    // 条件判断 -> 成功输出
    {
      id: `edge-condition-success-${timestamp}`,
      source: `condition-${timestamp}`,
      target: `output-success-${timestamp}`,
      sourceHandle: 'success',
      targetHandle: 'result',
      label: 'object',
      animated: true
    },
    
    // 条件判断 -> 失败输出
    {
      id: `edge-condition-error-${timestamp}`,
      source: `condition-${timestamp}`,
      target: `output-error-${timestamp}`,
      sourceHandle: 'failure',
      targetHandle: 'error',
      label: 'object',
      animated: true
    }
  ];
  
  // 创建工作流定义
  const workflow: WorkflowDefinition = {
    id: `workflow-${timestamp}`,
    name: name || '数据处理工作流',
    description: description || '一个完整的数据处理工作流，包含数据输入、验证、处理、条件判断和结果输出',
    version: '1.0.0',
    nodes,
    edges,
    metadata: {
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      author: 'system',
      tags: ['默认模板', '数据处理', '完整流程'],
      category: '通用模板'
    },
    config: {
      timeout: 300000, // 5分钟
      retryCount: 3,
      parallel: false,
      variables: {
        maxRetries: 3,
        timeout: 30000,
        enableLogging: true
      }
    }
  };
  
  return workflow;
}

/**
 * 创建简单工作流模板
 * 包含基本的输入 -> 处理 -> 输出流程
 */
export function createSimpleWorkflowTemplate(name: string, description?: string): WorkflowDefinition {
  const timestamp = Date.now();
  
  const nodes: NodeData[] = [
    // 输入节点
    {
      id: `input-${timestamp}`,
      type: 'input',
      label: '输入',
      icon: 'i-mdi:text-box',
      config: {
        placeholder: '请输入数据',
        multiline: false,
        maxLength: 1000
      },
      inputs: [],
      outputs: [
        {
          id: 'data',
          name: '数据',
          type: 'string',
          required: true,
          description: '输入的数据'
        }
      ],
      position: { x: 200, y: 200 },
      metadata: {
        description: '数据输入节点',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 处理节点
    {
      id: `process-${timestamp}`,
      type: 'process',
      label: '处理',
      icon: 'i-mdi:cog',
      config: {
        operation: 'transform'
      },
      inputs: [
        {
          id: 'input',
          name: '输入',
          type: 'string',
          required: true,
          description: '需要处理的数据'
        }
      ],
      outputs: [
        {
          id: 'output',
          name: '输出',
          type: 'string',
          required: true,
          description: '处理后的数据'
        }
      ],
      position: { x: 500, y: 200 },
      metadata: {
        description: '数据处理节点',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    },
    
    // 输出节点
    {
      id: `output-${timestamp}`,
      type: 'output',
      label: '输出',
      icon: 'i-mdi:export',
      config: {
        format: 'text'
      },
      inputs: [
        {
          id: 'result',
          name: '结果',
          type: 'string',
          required: true,
          description: '最终结果'
        }
      ],
      outputs: [],
      position: { x: 800, y: 200 },
      metadata: {
        description: '结果输出节点',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    }
  ];
  
  const edges: EdgeData[] = [
    {
      id: `edge-1-${timestamp}`,
      source: `input-${timestamp}`,
      target: `process-${timestamp}`,
      sourceHandle: 'data',
      targetHandle: 'input',
      animated: true
    },
    {
      id: `edge-2-${timestamp}`,
      source: `process-${timestamp}`,
      target: `output-${timestamp}`,
      sourceHandle: 'output',
      targetHandle: 'result',
      animated: true
    }
  ];
  
  return {
    id: `workflow-${timestamp}`,
    name: name || '简单工作流',
    description: description || '一个简单的三步工作流：输入 -> 处理 -> 输出',
    version: '1.0.0',
    nodes,
    edges,
    metadata: {
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      author: 'system',
      tags: ['简单模板', '基础流程'],
      category: '基础模板'
    },
    config: {
      timeout: 60000,
      retryCount: 1,
      parallel: false,
      variables: {}
    }
  };
}
