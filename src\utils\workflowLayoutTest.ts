// 工作流布局测试工具
import { createDefaultWorkflowTemplate, createSimpleWorkflowTemplate } from '@/components/workflow/templates/defaultWorkflowTemplate';

export function testWorkflowLayouts() {
  console.log('=== 工作流布局测试 ===');
  
  // 测试完整工作流模板
  const defaultWorkflow = createDefaultWorkflowTemplate('测试完整工作流');
  console.log('完整工作流节点位置:');
  defaultWorkflow.nodes.forEach(node => {
    console.log(`${node.label}: (${node.position.x}, ${node.position.y})`);
  });
  
  // 计算节点边界
  const positions = defaultWorkflow.nodes.map(node => node.position);
  const minX = Math.min(...positions.map(p => p.x));
  const maxX = Math.max(...positions.map(p => p.x));
  const minY = Math.min(...positions.map(p => p.y));
  const maxY = Math.max(...positions.map(p => p.y));
  
  console.log(`完整工作流边界: X(${minX} - ${maxX}), Y(${minY} - ${maxY})`);
  console.log(`工作流尺寸: ${maxX - minX} x ${maxY - minY}`);
  
  // 测试简单工作流模板
  const simpleWorkflow = createSimpleWorkflowTemplate('测试简单工作流');
  console.log('\n简单工作流节点位置:');
  simpleWorkflow.nodes.forEach(node => {
    console.log(`${node.label}: (${node.position.x}, ${node.position.y})`);
  });
  
  const simplePositions = simpleWorkflow.nodes.map(node => node.position);
  const simpleMinX = Math.min(...simplePositions.map(p => p.x));
  const simpleMaxX = Math.max(...simplePositions.map(p => p.x));
  const simpleMinY = Math.min(...simplePositions.map(p => p.y));
  const simpleMaxY = Math.max(...simplePositions.map(p => p.y));
  
  console.log(`简单工作流边界: X(${simpleMinX} - ${simpleMaxX}), Y(${simpleMinY} - ${simpleMaxY})`);
  console.log(`工作流尺寸: ${simpleMaxX - simpleMinX} x ${simpleMaxY - simpleMinY}`);
  
  return {
    defaultWorkflow,
    simpleWorkflow,
    defaultBounds: { minX, maxX, minY, maxY, width: maxX - minX, height: maxY - minY },
    simpleBounds: { 
      minX: simpleMinX, 
      maxX: simpleMaxX, 
      minY: simpleMinY, 
      maxY: simpleMaxY, 
      width: simpleMaxX - simpleMinX, 
      height: simpleMaxY - simpleMinY 
    }
  };
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  (window as any).testWorkflowLayouts = testWorkflowLayouts;
}
