# 工作流自动模板功能实现

## 功能概述

实现了在新建工作流时自动创建完整工作流模板的功能，用户可以选择不同类型的模板来快速开始工作流设计。

## 主要特性

### 1. 三种工作流模板

#### 完整工作流模板 (default)
- **包含节点**：
  - 数据输入节点：接收用户输入的数据
  - 数据验证节点：验证输入数据的格式和有效性
  - 数据处理节点：对验证后的数据进行转换和处理
  - 条件判断节点：根据处理结果决定后续流程
  - 成功输出节点：输出处理成功的结果
  - 错误输出节点：输出处理失败的错误信息

- **连接关系**：
  - 输入 → 验证 → 处理 → 条件判断 → 成功/失败输出
  - 形成完整的数据处理流水线

#### 简单工作流模板 (simple)
- **包含节点**：
  - 输入节点：数据输入
  - 处理节点：数据处理
  - 输出节点：结果输出

- **连接关系**：
  - 输入 → 处理 → 输出
  - 基础的三步工作流

#### 空白工作流 (empty)
- 不包含任何节点和连接
- 用户可以从零开始设计工作流

### 2. 用户界面改进

#### 工作流页面 (`/project/:id/workflow`)
- 添加了"新建模板"下拉菜单
- 用户可以选择不同的模板类型
- 页面默认加载完整工作流模板

#### 模板选择功能
- 下拉菜单包含三个选项：
  - 完整工作流模板
  - 简单工作流模板
  - 空白工作流
- 选择后立即创建对应的工作流模板

### 3. 测试页面

创建了专门的测试页面 (`/workflow-template-test`) 用于验证模板功能：
- 可以测试三种不同的模板创建
- 显示模板的详细信息（节点数量、连接数量等）
- 集成了工作流编辑器进行实时预览

## 技术实现

### 1. 模板定义文件
**文件位置**：`src/components/workflow/templates/defaultWorkflowTemplate.ts`

- `createDefaultWorkflowTemplate()`: 创建完整工作流模板
- `createSimpleWorkflowTemplate()`: 创建简单工作流模板
- 包含完整的节点定义、连接关系和元数据

### 2. 工作流服务增强
**文件位置**：`src/composables/useWorkflow.ts`

- 修改 `createWorkflow()` 函数，支持模板参数
- 新增 `useTemplate` 参数：`'default' | 'simple' | 'empty'`
- 默认使用完整模板

### 3. 工作流编辑器改进
**文件位置**：`src/components/workflow/WorkflowEditor.vue`

- 添加了 `watch` 监听 `modelValue` 变化
- 支持动态加载新的工作流数据
- 修复了模板切换时的数据更新问题

### 4. 页面组件更新
**文件位置**：`src/views/project/project-detail/workflow.vue`

- 添加模板选择下拉菜单
- 实现 `handleTemplateCommand()` 处理模板选择
- 修改初始化逻辑，默认使用完整模板

## 节点详细配置

### 数据输入节点
```typescript
{
  type: 'input',
  label: '数据输入',
  config: {
    placeholder: '请输入要处理的数据',
    multiline: true,
    maxLength: 5000,
    dataType: 'text'
  },
  outputs: [
    {
      id: 'data',
      name: '输入数据',
      type: 'string',
      required: true
    }
  ]
}
```

### 数据验证节点
```typescript
{
  type: 'process',
  label: '数据验证',
  config: {
    validationType: 'format',
    required: true,
    minLength: 1,
    maxLength: 1000
  },
  inputs: ['input_data'],
  outputs: ['validated_data', 'validation_result']
}
```

### 数据处理节点
```typescript
{
  type: 'process',
  label: '数据处理',
  config: {
    processType: 'transform',
    operation: 'format',
    outputFormat: 'json',
    enableCache: true
  },
  inputs: ['raw_data'],
  outputs: ['processed_data', 'metadata']
}
```

### 条件判断节点
```typescript
{
  type: 'condition',
  label: '结果判断',
  config: {
    conditionType: 'expression',
    expression: 'processed_data.length > 0',
    trueLabel: '处理成功',
    falseLabel: '处理失败'
  },
  inputs: ['data'],
  outputs: ['success', 'failure']
}
```

## 使用方法

### 1. 在工作流页面
1. 进入项目的工作流页面
2. 页面会自动加载完整工作流模板
3. 点击"新建模板"下拉菜单可以切换其他模板
4. 选择模板后立即生效

### 2. 在测试页面
1. 访问 `/workflow-template-test`
2. 点击不同的按钮测试各种模板
3. 查看模板信息和节点布局
4. 在编辑器中进行进一步编辑

### 3. 编程方式
```typescript
import { useWorkflow } from '@/composables/useWorkflow';

const { createWorkflow } = useWorkflow();

// 创建完整模板
const fullWorkflow = createWorkflow('我的工作流', '描述', 'default');

// 创建简单模板
const simpleWorkflow = createWorkflow('简单工作流', '描述', 'simple');

// 创建空白工作流
const emptyWorkflow = createWorkflow('空白工作流', '描述', 'empty');
```

## 优势

1. **快速开始**：用户无需从零开始设计工作流
2. **最佳实践**：模板体现了工作流设计的最佳实践
3. **灵活选择**：提供不同复杂度的模板满足不同需求
4. **易于修改**：用户可以基于模板进行个性化调整
5. **学习价值**：新用户可以通过模板学习工作流设计

## 视图和布局优化

### 1. 节点间距调整
- **完整工作流模板**：节点间距为 300px，确保连接线清晰可见
- **简单工作流模板**：节点间距为 300px，保持一致的视觉体验
- **节点位置**：
  - 输入节点：(50, 250)
  - 验证节点：(350, 250)
  - 处理节点：(650, 250)
  - 条件节点：(950, 250)
  - 成功输出：(1250, 150)
  - 错误输出：(1250, 350)

### 2. 画布视图优化
- **默认缩放**：设置为 0.6，确保完整工作流在画布中可见
- **最小缩放**：调整为 0.1，支持更大范围的缩小
- **自动适应**：加载新模板时自动调用 `fitView` 适应视图

### 3. 用户交互改进
- **适应视图按钮**：在工具栏添加"适应视图"按钮
- **自动布局**：新建模板时自动适应最佳视图
- **平滑动画**：视图切换使用 800ms 的平滑动画

### 4. 布局测试工具
- 创建了 `workflowLayoutTest.ts` 工具
- 可以测试和验证节点布局
- 在测试页面添加"测试布局"按钮

## 后续扩展

1. **更多模板**：可以添加更多专业领域的工作流模板
2. **模板分类**：按行业或用途对模板进行分类
3. **自定义模板**：允许用户保存和分享自己的模板
4. **模板市场**：建立模板分享和下载平台
5. **智能推荐**：根据用户需求推荐合适的模板
6. **自动布局算法**：实现更智能的节点自动布局
7. **响应式布局**：根据画布大小自动调整节点位置

## 文件清单

### 核心文件
- `src/components/workflow/templates/defaultWorkflowTemplate.ts` - 模板定义
- `src/composables/useWorkflow.ts` - 工作流服务
- `src/components/workflow/WorkflowEditor.vue` - 工作流编辑器
- `src/views/project/project-detail/workflow.vue` - 工作流页面

### 测试和工具
- `src/views/workflow-template-test.vue` - 测试页面
- `src/utils/workflowLayoutTest.ts` - 布局测试工具

### 文档
- `docs/workflow-template-feature.md` - 功能文档

## 更新日志

### v1.1.0 - 布局优化
- ✅ 调整节点间距，确保连接线清晰可见
- ✅ 优化默认画布缩放比例
- ✅ 添加"适应视图"功能按钮
- ✅ 实现自动视图适应
- ✅ 创建布局测试工具

### v1.0.0 - 基础功能
- ✅ 实现三种工作流模板
- ✅ 添加模板选择界面
- ✅ 创建完整的默认工作流
- ✅ 支持动态模板切换
